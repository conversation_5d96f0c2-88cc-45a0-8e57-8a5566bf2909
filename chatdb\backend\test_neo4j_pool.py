#!/usr/bin/env python3
"""
测试Neo4j连接池
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_neo4j_pool():
    """测试Neo4j连接池功能"""
    try:
        print("🔍 开始测试Neo4j连接池...")
        
        # 导入连接池
        from app.services.neo4j_connection_pool import get_neo4j_pool
        
        # 获取连接池实例
        print("1️⃣ 获取连接池实例...")
        pool = await get_neo4j_pool()
        print("✅ 连接池实例获取成功")
        
        # 测试基本查询
        print("2️⃣ 测试基本查询...")
        result = await pool.execute_read_query('RETURN 1 as test_value')
        print(f"✅ 基本查询成功: {result}")
        
        # 测试数据库信息查询
        print("3️⃣ 测试数据库信息查询...")
        db_info = await pool.execute_read_query('CALL db.info()')
        print(f"✅ 数据库信息查询成功: {len(db_info)} 条记录")
        
        # 测试节点查询
        print("4️⃣ 测试节点查询...")
        nodes = await pool.execute_read_query('MATCH (n) RETURN count(n) as node_count LIMIT 1')
        print(f"✅ 节点查询成功: {nodes}")
        
        print("🎉 所有测试通过！Neo4j连接池工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_neo4j_pool())
    sys.exit(0 if success else 1)
