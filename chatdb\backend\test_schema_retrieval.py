#!/usr/bin/env python3
"""
测试表结构检索功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_schema_retrieval():
    """测试表结构检索功能"""
    try:
        print("🔍 开始测试表结构检索功能...")
        
        # 导入必要的模块
        from app.services.neo4j_connection_pool import get_neo4j_pool
        from app.db.session import SessionLocal
        
        # 获取连接池实例
        print("1️⃣ 获取Neo4j连接池...")
        pool = await get_neo4j_pool()
        print("✅ Neo4j连接池获取成功")
        
        # 查询所有连接ID
        print("2️⃣ 查询可用的连接ID...")
        connections = await pool.execute_read_query(
            'MATCH (t:Table) RETURN DISTINCT t.connection_id as connection_id ORDER BY connection_id'
        )
        print(f"✅ 找到连接ID: {[c['connection_id'] for c in connections]}")
        
        if not connections:
            print("⚠️ 没有找到任何连接ID，无法继续测试")
            return False
            
        # 使用第一个连接ID进行测试
        connection_id = connections[0]['connection_id']
        print(f"3️⃣ 使用连接ID {connection_id} 测试表结构检索...")
        
        # 查询该连接的所有表
        tables = await pool.execute_read_query(
            'MATCH (t:Table {connection_id: $connection_id}) RETURN t.id as id, t.name as name, t.description as description',
            {'connection_id': connection_id}
        )
        print(f"✅ 找到 {len(tables)} 个表:")
        for table in tables[:5]:  # 只显示前5个
            print(f"   - ID: {table['id']}, 名称: {table['name']}, 描述: {table['description']}")
        
        # 测试增强缓存服务
        print("4️⃣ 测试增强缓存服务...")
        from app.services.enhanced_cache_service import enhanced_cache
        
        test_query = "查询销售数据"
        cached_result = await enhanced_cache.get_schema_context_cached(connection_id, test_query)
        print(f"✅ 缓存服务测试成功，返回 {len(cached_result.get('tables', []))} 个表")
        
        # 测试完整的表结构检索流程
        print("5️⃣ 测试完整的表结构检索流程...")
        from app.services.text2sql_utils import retrieve_relevant_schema
        
        db = SessionLocal()
        try:
            schema_context = await retrieve_relevant_schema(db, connection_id, test_query)
            print(f"✅ 表结构检索成功，返回 {len(schema_context.get('tables', []))} 个相关表")
            
            if schema_context.get('tables'):
                print("   相关表:")
                for table in schema_context['tables'][:3]:  # 只显示前3个
                    print(f"   - {table.get('name', 'Unknown')}")
        finally:
            db.close()
        
        print("🎉 所有测试通过！表结构检索功能工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_schema_retrieval())
    sys.exit(0 if success else 1)
