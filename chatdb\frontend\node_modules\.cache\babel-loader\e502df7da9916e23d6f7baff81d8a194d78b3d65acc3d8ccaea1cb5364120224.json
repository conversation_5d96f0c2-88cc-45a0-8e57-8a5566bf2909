{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\text2sql\\\\utils.tsx\";\nimport React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { nord as codeTheme } from 'react-syntax-highlighter/dist/esm/styles/prism';\n// 暂时注释掉 remarkGfm 以避免类型错误\n// import remarkGfm from 'remark-gfm';\n\n/**\n * 将数据转换为CSV格式的函数\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const convertToCSV = data => {\n  if (!data || !data.length) return '';\n\n  // 获取所有列名\n  const headers = Object.keys(data[0]);\n\n  // 创建CSV头行\n  const headerRow = headers.join(',');\n\n  // 创建数据行\n  const rows = data.map(row => {\n    return headers.map(header => {\n      // 处理值中的特殊字符，如逗号、引号等\n      const value = row[header];\n      const valueStr = value === null || value === undefined ? '' : String(value);\n      // 如果值包含逗号、引号或换行符，则用引号包裹并处理内部引号\n      if (valueStr.includes(',') || valueStr.includes('\"') || valueStr.includes('\\n')) {\n        return `\"${valueStr.replace(/\"/g, '\"\"')}\"`;\n      }\n      return valueStr;\n    }).join(',');\n  }).join('\\n');\n\n  // 返回完整的CSV内容\n  return `${headerRow}\\n${rows}`;\n};\n\n/**\n * 格式化文本展示组件 - 优化版\n */\nexport const FormattedOutput = ({\n  content,\n  type,\n  region\n}) => {\n  if (!content) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-400 italic text-center p-2\",\n      children: \"\\u6682\\u65E0\\u5185\\u5BB9\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 预处理内容，修复常见的格式问题\n  const preprocessContent = (rawContent, contentType) => {\n    let processed = rawContent;\n\n    // 对Markdown内容进行特殊处理（对语句解释区域进行轻量化处理）\n    if (contentType === 'markdown') {\n      // 如果是语句解释区域，只进行基本的格式清理\n      if (region === 'explanation') {\n        // 只进行最基本的清理，保持markdown格式完整性\n        processed = processed.replace(/\\n{3,}/g, '\\n\\n'); // 清理多余空行\n        processed = processed.replace(/^\\s+|\\s+$/g, ''); // 清理首尾空白\n        return processed;\n      }\n      // 首先移除用户交互提示文本，但保留用户反馈内容\n      processed = processed.replace(/Enter your response: 请输入修改建议或者直接点击同意(?=\\s|$)/g, '');\n      // 不完全移除用户已同意操作的标记，保留用户反馈部分\n      processed = processed.replace(/---+\\n### 用户已同意操作\\n---+(?!\\n### 用户反馈)/g, '---\\n### 用户已同意操作\\n---');\n      processed = processed.replace(/分析已完成/g, '');\n\n      // 先清理重复的内容\n      processed = processed.replace(/(已找到以下相关表:[\\s\\S]*?)\\1/g, '$1');\n      processed = processed.replace(/(# SQL 命令生成报告)[\\s\\S]*?### 1\\\\./g, '$1\\n\\n### 1\\\\.');\n      processed = processed.replace(/生成报告\\s*\\n/g, '');\n\n      // 修复特定的格式问题 - 添加换行\n      processed = processed.replace(/正在分析查询并获取相关表结构\\.\\.\\./g, '正在分析查询并获取相关表结构...\\n');\n      processed = processed.replace(/已找到以下相关表:/g, '已找到以下相关表:\\n');\n      processed = processed.replace(/表结构检索完成正在分析查询意图\\.\\.\\./g, '表结构检索完成\\n正在分析查询意图...\\n');\n\n      // 修复重复的标题问题\n      processed = processed.replace(/### (\\d+\\.)### \\1/g, '### $1');\n      processed = processed.replace(/### (\\d+\\.) \\. /g, '### $1 ');\n      processed = processed.replace(/(\\d+\\.) \\1/g, '$1');\n      processed = processed.replace(/\\.(\\d+)\\./g, '.$1');\n      processed = processed.replace(/###\\s*\\n###/g, '###');\n      processed = processed.replace(/值 值映射信息映射信息/g, '值映射信息');\n      processed = processed.replace(/查询意图描述意图描述/g, '查询意图描述');\n      processed = processed.replace(/需要使用的表使用的表名列表/g, '需要使用的表名列表');\n      processed = processed.replace(/筛选条件筛选条件描述/g, '筛选条件描述');\n      processed = processed.replace(/分组描述\\s*\\n\\s*分组描述/g, '分组描述');\n      processed = processed.replace(/排序描述\\s*\\n\\s*排序描述/g, '排序描述');\n      processed = processed.replace(/潜在歧义 潜在歧义与缺失与缺失信息/g, '潜在歧义与缺失信息');\n      processed = processed.replace(/初步 初步的SQL的SQL查询结构草案/g, '初步的SQL查询结构草案');\n      processed = processed.replace(/基于 基于以上分析的初步 SQL 查询 查询结构/g, '基于以上分析的初步SQL查询结构');\n\n      // 修复数据库结构中的错误\n      processed = processed.replace(/CREATE TABLE \\[students\\s*CREATE TABLE \\[students\\]/g, 'CREATE TABLE [students]');\n      processed = processed.replace(/\\]\\s*\\n\\s*\\(\\s*\\n\\s*\\[\\s*\\]\\s*\\n\\s*\\(/g, ']\\n(');\n      processed = processed.replace(/student_id\\] INTEGER PRIMARY KEYstudent_id\\] INTEGER PRIMARY KEY/g, 'student_id] INTEGER PRIMARY KEY');\n      processed = processed.replace(/student ,\\s*\\[student/g, 'student_id ,\\n   [student');\n      processed = processed.replace(/_name\\] VARCHAR\\(_name\\] VARCHAR\\(/g, '_name] VARCHAR(');\n      processed = processed.replace(/100\\) 100\\)/g, '100)');\n      processed = processed.replace(/major\\] VARCHAR\\(100\\)major\\] VARCHAR\\(100\\)/g, 'major] VARCHAR(100)');\n      processed = processed.replace(/\\[   \\[major\\]/g, 'major');\n      processed = processed.replace(/\\[year_ofyear_of_enrollment\\]/g, 'year_of_enrollment');\n      processed = processed.replace(/enrollment\\] INTEGER ,\\s*\\[ ,/g, 'enrollment] INTEGER ,');\n      processed = processed.replace(/student_agestudent_age\\] INTEGER\\] INTEGER/g, 'student_age] INTEGER');\n      processed = processed.replace(/\\);\\s*,\\s*\\);/g, ');');\n      processed = processed.replace(/\\[student_idstudent_id\\] INTEGER PRIMARY KEY FOREIGN KEY FOREIGN KEY,/g, '[student_id] INTEGER PRIMARY KEY FOREIGN KEY,');\n      processed = processed.replace(/,\\s*,/g, ',');\n      processed = processed.replace(/\\[course \\[course_id\\]/g, '[course_id]');\n      processed = processed.replace(/\\[   \\[score\\]/g, '[score]');\n      processed = processed.replace(/\\[sem \\[semester\\]/g, '[semester]');\n\n      // 修复SQL语句中的错误\n      processed = processed.replace(/SELECT\\s*\\n\\s*SELECT/g, 'SELECT');\n      processed = processed.replace(/SELECT student(SELECT student)?_id,_id,/g, 'SELECT student_id,');\n      processed = processed.replace(/student_name student_name/g, 'student_name');\n      processed = processed.replace(/year, year_of_enrollment/g, 'year_of_enrollment');\n      processed = processed.replace(/student_age student_age/g, 'student_age');\n      processed = processed.replace(/FROM students\\s*\\n\\s*FROM students/g, 'FROM students');\n      processed = processed.replace(/\\[([^\\]]+)\\]/g, '$1'); // 移除方括号\n\n      // 修复字段列表中的错误\n      processed = processed.replace(/students: student: student_id,/g, 'students: student_id,');\n      processed = processed.replace(/student_name, major student_name, major/g, 'student_name, major');\n      processed = processed.replace(/, year_of_en_of_enrollment,/g, ', year_of_enrollment,');\n      processed = processed.replace(/rollment, student_age student_age/g, 'student_age');\n\n      // 修复重复的段落\n      processed = processed.replace(/(获取所有学生的基本信息)\\s*\\n\\s*\\1/g, '$1');\n      processed = processed.replace(/(mysql)\\s*\\n\\s*###\\s*\\n\\s*###\\s*\\n\\s*### 3/g, '$1\\n\\n### 3');\n      processed = processed.replace(/###\\s*\\n\\s*###/g, '###');\n\n      // 修复特殊的格式问题\n      processed = processed.replace(/\\(\\s*\\n\\s*\\(/g, '(');\n      processed = processed.replace(/\\)\\s*\\n\\s*\\)/g, ')');\n      processed = processed.replace(/\\(\\s*\\n\\s*不需要使用/g, '(不需要使用');\n      processed = processed.replace(/聚合函数\\s*\\n\\s*\\)/g, '聚合函数)');\n      processed = processed.replace(/\\n-\\s*\\n-/g, '\\n-');\n      processed = processed.replace(/\\n\\s*\\n-\\s*\\n/g, '\\n\\n- ');\n      processed = processed.replace(/\\n-\\s*\\n\\s*-/g, '\\n- ');\n      processed = processed.replace(/\\n\\s*\\n\\s*###/g, '\\n\\n###');\n      processed = processed.replace(/\\n\\s*\\n\\s*```/g, '\\n\\n```');\n      processed = processed.replace(/```\\s*\\n\\s*\\n/g, '```\\n');\n      processed = processed.replace(/;\\s*\\n\\s*```Enter/g, ';\\n```\\n\\nEnter');\n\n      // 移除多余的分析已完成文本\n      processed = processed.replace(/分析已完成# SQL 命令生成报告/g, '# SQL 命令生成报告');\n\n      // 1. 修复连续的换行问题，确保段落之间有空行\n      processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n\n      // 2. 修复标题前后的空行\n      processed = processed.replace(/(\\n)(#{1,6}\\s+[^\\n]+)/g, '$1\\n$2');\n      processed = processed.replace(/(#{1,6}\\s+[^\\n]+)(\\n)/g, '$1\\n$2');\n\n      // 3. 修复列表项的格式\n      processed = processed.replace(/(\\n)([*-]\\s+[^\\n]+)(\\n)(?![*-]\\s+)/g, '$1$2\\n$3');\n\n      // 4. 修复代码块的格式\n      processed = processed.replace(/(```[^\\n]*)(\\n)/g, '$1\\n$2');\n      processed = processed.replace(/(\\n)(```)(\\n)/g, '$1$2\\n$3');\n\n      // 5. 修复表格的格式\n      processed = processed.replace(/\\|\\s*\\n\\s*\\|/g, '|\\n|');\n\n      // 6. 修复数字列表标题格式，确保正确渲染\n      processed = processed.replace(/(\\n|^)(\\d+\\.)\\s+([^\\n]+)/g, '$1$2 $3');\n\n      // 7. 修复标题格式，确保与数字之间有空格\n      processed = processed.replace(/(#{1,6})(\\d+\\.)/g, '$1 $2');\n\n      // 最后执行一次全面清理\n      // 删除重复的段落\n      processed = processed.replace(/(获取所有学生的基本信息)\\s*\\n\\s*\\1/g, '$1');\n      processed = processed.replace(/(获取所有学生的基本信息)\\s*\\n\\s*\\n\\s*\\1/g, '$1');\n      processed = processed.replace(/(mysql)\\s*\\n\\s*###\\s*\\n\\s*###\\s*\\n\\s*### 3/g, '$1\\n\\n### 3');\n      processed = processed.replace(/(mysql)\\s*\\n\\s*\\n\\s*###\\s*\\n\\s*###\\s*\\n\\s*### 3/g, '$1\\n\\n### 3');\n\n      // 清理重复的标题\n      processed = processed.replace(/###\\s*\\n\\s*###/g, '###');\n      processed = processed.replace(/###\\s*\\n\\s*\\n\\s*###/g, '###\\n\\n');\n\n      // 清理重复的内容\n      processed = processed.replace(/(已找到以下相关表:[\\s\\S]*?)\\1/g, '$1');\n      processed = processed.replace(/(# SQL 命令生成报告)[\\s\\S]*?### 1\\./g, '$1\\n\\n### 1\\.');\n\n      // 清理重复的字段列表\n      processed = processed.replace(/students: student_id, student_name, major, year_of_enrollment, student_age\\s*\\n\\s*\\n\\s*\\(\\s*\\n\\s*\\(/g, 'students: student_id, student_name, major, year_of_enrollment, student_age\\n\\n(');\n\n      // 清理重复的表连接描述\n      processed = processed.replace(/- 不需要表连接\\uff0c连接\\uff0c因为所有需要因为所有需要的信息都在students表中students表中/g, '- 不需要表连接，因为所有需要的信息都在students表中');\n\n      // 清理重复的筛选条件\n      processed = processed.replace(/- 无筛选条件\\uff0c需要返回所有学生记录\\u3002\\s*\\n\\s*###\\s*\\n\\s*### 10\\. 10\\./g, '- 无筛选条件，需要返回所有学生记录。\\n\\n### 10.');\n\n      // 清理重复的分组描述\n      processed = processed.replace(/- 不需要分组不需要分组操作\\u3002\\s*\\n\\s*###\\s*\\n\\s*操作\\u3002\\s*\\n\\s*###\\s*\\n\\s*### 11\\./g, '- 不需要分组操作。\\n\\n### 11.');\n\n      // 清理重复的排序描述\n      processed = processed.replace(/- 用户没有用户没有指定排序要求，要求，可以按默认可以按默认顺序返回顺序返回结果（结果（通常为主通常为主键顺序）\\u3002\\s*\\n\\s*###\\s*\\n\\s*键顺序）\\u3002\\s*\\n\\s*###\\s*\\n\\s*### 12/g, '- 用户没有指定排序要求，可以按默认顺序返回结果（通常为主键顺序）。\\n\\n### 12');\n\n      // 清理SQL语句中的错误\n      processed = processed.replace(/```sql\\s*([^`]*)```/g, (match, sqlContent) => {\n        // 清理SQL代码块内的格式问题\n        let cleanedSql = sqlContent.replace(/\\s*SELECT\\s+SELECT\\s*/g, 'SELECT ').replace(/\\s*FROM\\s+FROM\\s*/g, 'FROM ').replace(/(\\w+)\\s+\\1/g, '$1').replace(/\\s+;\\s+/g, ';\\n').replace(/--\\s*\\n\\s*--/g, '--').replace(/SELECT\\s*\\n\\s*SELECT/g, 'SELECT').replace(/FROM students\\s*\\n\\s*FROM students/g, 'FROM students').replace(/student_id student_id/g, 'student_id').replace(/student_name, student_name/g, 'student_name').replace(/major, major/g, 'major').replace(/year_of_enrollment, year_of_enrollment/g, 'year_of_enrollment').replace(/student_age\\s*\\n\\s*FROM/g, 'student_age\\nFROM').trim();\n        return '```sql\\n' + cleanedSql + '\\n```';\n      });\n\n      // 最后清理多余的空行\n      processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n      processed = processed.replace(/```\\s*\\n\\s*\\n/g, '```\\n');\n      processed = processed.replace(/\\n\\s*\\n```/g, '\\n```');\n      processed = processed.replace(/;\\s*\\n\\s*```Enter/g, ';\\n```\\n\\nEnter');\n    }\n    return processed;\n  };\n\n  // 预处理内容\n  const processedContent = preprocessContent(content, type);\n  try {\n    switch (type) {\n      case 'json':\n        try {\n          // 尝试解析JSON\n          const parsedJson = JSON.parse(processedContent);\n          return /*#__PURE__*/_jsxDEV(SyntaxHighlighter, {\n            language: \"json\",\n            style: codeTheme,\n            showLineNumbers: true,\n            startingLineNumber: 1,\n            wrapLines: true,\n            wrapLongLines: true,\n            children: JSON.stringify(parsedJson, null, 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this);\n        } catch (e) {\n          // 如果解析失败，作为普通文本显示\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: processedContent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 18\n          }, this);\n        }\n      case 'sql':\n        return /*#__PURE__*/_jsxDEV(SyntaxHighlighter, {\n          language: \"sql\",\n          style: codeTheme,\n          showLineNumbers: true,\n          startingLineNumber: 1,\n          wrapLines: true,\n          wrapLongLines: true,\n          children: processedContent\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this);\n      case 'markdown':\n        try {\n          return /*#__PURE__*/_jsxDEV(ReactMarkdown\n          // 暂时移除 remarkPlugins 以避免类型错误\n          // remarkPlugins={[remarkGfm]}\n          , {\n            components: {\n              pre({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"rounded-md bg-gray-100 dark:bg-gray-800/70 p-2 my-4 overflow-auto\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 26\n                }, this);\n              },\n              code({\n                node,\n                className,\n                ...props\n              }) {\n                const match = /language-(\\w+)/.exec(className || '');\n                return !props.inline && match ? /*#__PURE__*/_jsxDEV(SyntaxHighlighter, {\n                  language: match[1],\n                  style: codeTheme,\n                  showLineNumbers: true,\n                  startingLineNumber: 1,\n                  PreTag: \"div\",\n                  wrapLines: true,\n                  wrapLongLines: true,\n                  children: String(props.children).replace(/\\n$/, '')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: `${className || ''} px-1 py-0.5 rounded bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 text-sm font-mono`,\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this);\n              },\n              table({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto my-6 rounded-md border border-gray-200 dark:border-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-700\",\n                    ...props\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this);\n              },\n              thead({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50 dark:bg-gray-800\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 26\n                }, this);\n              },\n              th({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 26\n                }, this);\n              },\n              td({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-3 text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 26\n                }, this);\n              },\n              h1({\n                node,\n                children,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl font-bold mt-6 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700\",\n                  ...props,\n                  children: children\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 26\n                }, this);\n              },\n              h2({\n                node,\n                children,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-bold mt-5 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700\",\n                  ...props,\n                  children: children\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 26\n                }, this);\n              },\n              h3({\n                node,\n                children,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-bold mt-4 mb-2\",\n                  ...props,\n                  children: children\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 26\n                }, this);\n              },\n              h4({\n                node,\n                children,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-base font-semibold mt-3 mb-2\",\n                  ...props,\n                  children: children\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 26\n                }, this);\n              },\n              ul({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc pl-6 my-4 space-y-2\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 26\n                }, this);\n              },\n              ol({\n                node,\n                start,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"ol\", {\n                  className: \"list-decimal pl-6 my-4 space-y-2\",\n                  start: start || 1,\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 26\n                }, this);\n              },\n              li({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"my-1\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 26\n                }, this);\n              },\n              p({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"my-4 leading-relaxed\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 26\n                }, this);\n              },\n              blockquote({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"blockquote\", {\n                  className: \"pl-4 border-l-4 border-gray-200 dark:border-gray-700 my-4 italic text-gray-600 dark:text-gray-300\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 26\n                }, this);\n              },\n              hr({\n                node,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"hr\", {\n                  className: \"my-6 border-gray-200 dark:border-gray-700\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 26\n                }, this);\n              },\n              a({\n                node,\n                children,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"a\", {\n                  className: \"text-blue-600 dark:text-blue-400 hover:underline\",\n                  ...props,\n                  children: children\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 26\n                }, this);\n              },\n              img({\n                node,\n                alt,\n                ...props\n              }) {\n                return /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"max-w-full h-auto my-4 rounded-md\",\n                  alt: alt || \"图片\",\n                  ...props\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 26\n                }, this);\n              }\n            },\n            children: processedContent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this);\n        } catch (error) {\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"whitespace-pre-wrap p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800/50 rounded-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 dark:text-red-400 font-bold mb-2\",\n              children: \"Markdown\\u6E32\\u67D3\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-auto max-h-[300px] font-mono text-sm\",\n              children: processedContent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this);\n        }\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"whitespace-pre-wrap leading-relaxed\",\n          children: processedContent\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 16\n        }, this);\n    }\n  } catch (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"whitespace-pre-wrap text-red-500 p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800/50 rounded-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"font-bold mb-2\",\n        children: \"\\u6E32\\u67D3\\u9519\\u8BEF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-auto max-h-[300px] font-mono text-sm\",\n        children: processedContent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 12\n    }, this);\n  }\n};\n_c = FormattedOutput;\nvar _c;\n$RefreshReg$(_c, \"FormattedOutput\");", "map": {"version": 3, "names": ["React", "ReactMarkdown", "Prism", "Syntax<PERSON><PERSON><PERSON><PERSON>", "nord", "codeTheme", "jsxDEV", "_jsxDEV", "convertToCSV", "data", "length", "headers", "Object", "keys", "headerRow", "join", "rows", "map", "row", "header", "value", "valueStr", "undefined", "String", "includes", "replace", "FormattedOutput", "content", "type", "region", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "preprocessContent", "rawContent", "contentType", "processed", "match", "sqlContent", "cleanedSql", "trim", "processedContent", "parsedJson", "JSON", "parse", "language", "style", "showLineNumbers", "startingLineNumber", "wrapLines", "wrapLongLines", "stringify", "e", "components", "pre", "node", "props", "code", "exec", "inline", "PreTag", "table", "thead", "th", "td", "h1", "h2", "h3", "h4", "ul", "ol", "start", "li", "p", "blockquote", "hr", "a", "img", "alt", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/text2sql/utils.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { nord as codeTheme } from 'react-syntax-highlighter/dist/esm/styles/prism';\n// 暂时注释掉 remarkGfm 以避免类型错误\n// import remarkGfm from 'remark-gfm';\n\n/**\n * 将数据转换为CSV格式的函数\n */\nexport const convertToCSV = (data: any[]): string => {\n  if (!data || !data.length) return '';\n\n  // 获取所有列名\n  const headers = Object.keys(data[0]);\n\n  // 创建CSV头行\n  const headerRow = headers.join(',');\n\n  // 创建数据行\n  const rows = data.map(row => {\n    return headers.map(header => {\n      // 处理值中的特殊字符，如逗号、引号等\n      const value = row[header];\n      const valueStr = value === null || value === undefined ? '' : String(value);\n      // 如果值包含逗号、引号或换行符，则用引号包裹并处理内部引号\n      if (valueStr.includes(',') || valueStr.includes('\"') || valueStr.includes('\\n')) {\n        return `\"${valueStr.replace(/\"/g, '\"\"')}\"`;\n      }\n      return valueStr;\n    }).join(',');\n  }).join('\\n');\n\n  // 返回完整的CSV内容\n  return `${headerRow}\\n${rows}`;\n};\n\n/**\n * 格式化文本展示组件 - 优化版\n */\nexport const FormattedOutput = ({ content, type, region }: { content: string, type: 'sql' | 'json' | 'markdown' | 'text', region?: string }) => {\n  if (!content) {\n    return <div className=\"text-gray-400 italic text-center p-2\">暂无内容</div>;\n  }\n\n  // 预处理内容，修复常见的格式问题\n  const preprocessContent = (rawContent: string, contentType: string): string => {\n    let processed = rawContent;\n\n    // 对Markdown内容进行特殊处理（对语句解释区域进行轻量化处理）\n    if (contentType === 'markdown') {\n      // 如果是语句解释区域，只进行基本的格式清理\n      if (region === 'explanation') {\n        // 只进行最基本的清理，保持markdown格式完整性\n        processed = processed.replace(/\\n{3,}/g, '\\n\\n'); // 清理多余空行\n        processed = processed.replace(/^\\s+|\\s+$/g, ''); // 清理首尾空白\n        return processed;\n      }\n      // 首先移除用户交互提示文本，但保留用户反馈内容\n      processed = processed.replace(/Enter your response: 请输入修改建议或者直接点击同意(?=\\s|$)/g, '');\n      // 不完全移除用户已同意操作的标记，保留用户反馈部分\n      processed = processed.replace(/---+\\n### 用户已同意操作\\n---+(?!\\n### 用户反馈)/g, '---\\n### 用户已同意操作\\n---');\n      processed = processed.replace(/分析已完成/g, '');\n\n      // 先清理重复的内容\n      processed = processed.replace(/(已找到以下相关表:[\\s\\S]*?)\\1/g, '$1');\n      processed = processed.replace(/(# SQL 命令生成报告)[\\s\\S]*?### 1\\\\./g, '$1\\n\\n### 1\\\\.');\n      processed = processed.replace(/生成报告\\s*\\n/g, '');\n\n      // 修复特定的格式问题 - 添加换行\n      processed = processed.replace(/正在分析查询并获取相关表结构\\.\\.\\./g, '正在分析查询并获取相关表结构...\\n');\n      processed = processed.replace(/已找到以下相关表:/g, '已找到以下相关表:\\n');\n      processed = processed.replace(/表结构检索完成正在分析查询意图\\.\\.\\./g, '表结构检索完成\\n正在分析查询意图...\\n');\n\n      // 修复重复的标题问题\n      processed = processed.replace(/### (\\d+\\.)### \\1/g, '### $1');\n      processed = processed.replace(/### (\\d+\\.) \\. /g, '### $1 ');\n      processed = processed.replace(/(\\d+\\.) \\1/g, '$1');\n      processed = processed.replace(/\\.(\\d+)\\./g, '.$1');\n      processed = processed.replace(/###\\s*\\n###/g, '###');\n      processed = processed.replace(/值 值映射信息映射信息/g, '值映射信息');\n      processed = processed.replace(/查询意图描述意图描述/g, '查询意图描述');\n      processed = processed.replace(/需要使用的表使用的表名列表/g, '需要使用的表名列表');\n      processed = processed.replace(/筛选条件筛选条件描述/g, '筛选条件描述');\n      processed = processed.replace(/分组描述\\s*\\n\\s*分组描述/g, '分组描述');\n      processed = processed.replace(/排序描述\\s*\\n\\s*排序描述/g, '排序描述');\n      processed = processed.replace(/潜在歧义 潜在歧义与缺失与缺失信息/g, '潜在歧义与缺失信息');\n      processed = processed.replace(/初步 初步的SQL的SQL查询结构草案/g, '初步的SQL查询结构草案');\n      processed = processed.replace(/基于 基于以上分析的初步 SQL 查询 查询结构/g, '基于以上分析的初步SQL查询结构');\n\n      // 修复数据库结构中的错误\n      processed = processed.replace(/CREATE TABLE \\[students\\s*CREATE TABLE \\[students\\]/g, 'CREATE TABLE [students]');\n      processed = processed.replace(/\\]\\s*\\n\\s*\\(\\s*\\n\\s*\\[\\s*\\]\\s*\\n\\s*\\(/g, ']\\n(');\n      processed = processed.replace(/student_id\\] INTEGER PRIMARY KEYstudent_id\\] INTEGER PRIMARY KEY/g, 'student_id] INTEGER PRIMARY KEY');\n      processed = processed.replace(/student ,\\s*\\[student/g, 'student_id ,\\n   [student');\n      processed = processed.replace(/_name\\] VARCHAR\\(_name\\] VARCHAR\\(/g, '_name] VARCHAR(');\n      processed = processed.replace(/100\\) 100\\)/g, '100)');\n      processed = processed.replace(/major\\] VARCHAR\\(100\\)major\\] VARCHAR\\(100\\)/g, 'major] VARCHAR(100)');\n      processed = processed.replace(/\\[   \\[major\\]/g, 'major');\n      processed = processed.replace(/\\[year_ofyear_of_enrollment\\]/g, 'year_of_enrollment');\n      processed = processed.replace(/enrollment\\] INTEGER ,\\s*\\[ ,/g, 'enrollment] INTEGER ,');\n      processed = processed.replace(/student_agestudent_age\\] INTEGER\\] INTEGER/g, 'student_age] INTEGER');\n      processed = processed.replace(/\\);\\s*,\\s*\\);/g, ');');\n      processed = processed.replace(/\\[student_idstudent_id\\] INTEGER PRIMARY KEY FOREIGN KEY FOREIGN KEY,/g, '[student_id] INTEGER PRIMARY KEY FOREIGN KEY,');\n      processed = processed.replace(/,\\s*,/g, ',');\n      processed = processed.replace(/\\[course \\[course_id\\]/g, '[course_id]');\n      processed = processed.replace(/\\[   \\[score\\]/g, '[score]');\n      processed = processed.replace(/\\[sem \\[semester\\]/g, '[semester]');\n\n      // 修复SQL语句中的错误\n      processed = processed.replace(/SELECT\\s*\\n\\s*SELECT/g, 'SELECT');\n      processed = processed.replace(/SELECT student(SELECT student)?_id,_id,/g, 'SELECT student_id,');\n      processed = processed.replace(/student_name student_name/g, 'student_name');\n      processed = processed.replace(/year, year_of_enrollment/g, 'year_of_enrollment');\n      processed = processed.replace(/student_age student_age/g, 'student_age');\n      processed = processed.replace(/FROM students\\s*\\n\\s*FROM students/g, 'FROM students');\n      processed = processed.replace(/\\[([^\\]]+)\\]/g, '$1'); // 移除方括号\n\n      // 修复字段列表中的错误\n      processed = processed.replace(/students: student: student_id,/g, 'students: student_id,');\n      processed = processed.replace(/student_name, major student_name, major/g, 'student_name, major');\n      processed = processed.replace(/, year_of_en_of_enrollment,/g, ', year_of_enrollment,');\n      processed = processed.replace(/rollment, student_age student_age/g, 'student_age');\n\n      // 修复重复的段落\n      processed = processed.replace(/(获取所有学生的基本信息)\\s*\\n\\s*\\1/g, '$1');\n      processed = processed.replace(/(mysql)\\s*\\n\\s*###\\s*\\n\\s*###\\s*\\n\\s*### 3/g, '$1\\n\\n### 3');\n      processed = processed.replace(/###\\s*\\n\\s*###/g, '###');\n\n      // 修复特殊的格式问题\n      processed = processed.replace(/\\(\\s*\\n\\s*\\(/g, '(');\n      processed = processed.replace(/\\)\\s*\\n\\s*\\)/g, ')');\n      processed = processed.replace(/\\(\\s*\\n\\s*不需要使用/g, '(不需要使用');\n      processed = processed.replace(/聚合函数\\s*\\n\\s*\\)/g, '聚合函数)');\n      processed = processed.replace(/\\n-\\s*\\n-/g, '\\n-');\n      processed = processed.replace(/\\n\\s*\\n-\\s*\\n/g, '\\n\\n- ');\n      processed = processed.replace(/\\n-\\s*\\n\\s*-/g, '\\n- ');\n      processed = processed.replace(/\\n\\s*\\n\\s*###/g, '\\n\\n###');\n      processed = processed.replace(/\\n\\s*\\n\\s*```/g, '\\n\\n```');\n      processed = processed.replace(/```\\s*\\n\\s*\\n/g, '```\\n');\n      processed = processed.replace(/;\\s*\\n\\s*```Enter/g, ';\\n```\\n\\nEnter');\n\n      // 移除多余的分析已完成文本\n      processed = processed.replace(/分析已完成# SQL 命令生成报告/g, '# SQL 命令生成报告');\n\n      // 1. 修复连续的换行问题，确保段落之间有空行\n      processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n\n      // 2. 修复标题前后的空行\n      processed = processed.replace(/(\\n)(#{1,6}\\s+[^\\n]+)/g, '$1\\n$2');\n      processed = processed.replace(/(#{1,6}\\s+[^\\n]+)(\\n)/g, '$1\\n$2');\n\n      // 3. 修复列表项的格式\n      processed = processed.replace(/(\\n)([*-]\\s+[^\\n]+)(\\n)(?![*-]\\s+)/g, '$1$2\\n$3');\n\n      // 4. 修复代码块的格式\n      processed = processed.replace(/(```[^\\n]*)(\\n)/g, '$1\\n$2');\n      processed = processed.replace(/(\\n)(```)(\\n)/g, '$1$2\\n$3');\n\n      // 5. 修复表格的格式\n      processed = processed.replace(/\\|\\s*\\n\\s*\\|/g, '|\\n|');\n\n      // 6. 修复数字列表标题格式，确保正确渲染\n      processed = processed.replace(/(\\n|^)(\\d+\\.)\\s+([^\\n]+)/g, '$1$2 $3');\n\n      // 7. 修复标题格式，确保与数字之间有空格\n      processed = processed.replace(/(#{1,6})(\\d+\\.)/g, '$1 $2');\n\n      // 最后执行一次全面清理\n      // 删除重复的段落\n      processed = processed.replace(/(获取所有学生的基本信息)\\s*\\n\\s*\\1/g, '$1');\n      processed = processed.replace(/(获取所有学生的基本信息)\\s*\\n\\s*\\n\\s*\\1/g, '$1');\n      processed = processed.replace(/(mysql)\\s*\\n\\s*###\\s*\\n\\s*###\\s*\\n\\s*### 3/g, '$1\\n\\n### 3');\n      processed = processed.replace(/(mysql)\\s*\\n\\s*\\n\\s*###\\s*\\n\\s*###\\s*\\n\\s*### 3/g, '$1\\n\\n### 3');\n\n      // 清理重复的标题\n      processed = processed.replace(/###\\s*\\n\\s*###/g, '###');\n      processed = processed.replace(/###\\s*\\n\\s*\\n\\s*###/g, '###\\n\\n');\n\n      // 清理重复的内容\n      processed = processed.replace(/(已找到以下相关表:[\\s\\S]*?)\\1/g, '$1');\n      processed = processed.replace(/(# SQL 命令生成报告)[\\s\\S]*?### 1\\./g, '$1\\n\\n### 1\\.');\n\n      // 清理重复的字段列表\n      processed = processed.replace(/students: student_id, student_name, major, year_of_enrollment, student_age\\s*\\n\\s*\\n\\s*\\(\\s*\\n\\s*\\(/g, 'students: student_id, student_name, major, year_of_enrollment, student_age\\n\\n(');\n\n      // 清理重复的表连接描述\n      processed = processed.replace(/- 不需要表连接\\uff0c连接\\uff0c因为所有需要因为所有需要的信息都在students表中students表中/g, '- 不需要表连接，因为所有需要的信息都在students表中');\n\n      // 清理重复的筛选条件\n      processed = processed.replace(/- 无筛选条件\\uff0c需要返回所有学生记录\\u3002\\s*\\n\\s*###\\s*\\n\\s*### 10\\. 10\\./g, '- 无筛选条件，需要返回所有学生记录。\\n\\n### 10.');\n\n      // 清理重复的分组描述\n      processed = processed.replace(/- 不需要分组不需要分组操作\\u3002\\s*\\n\\s*###\\s*\\n\\s*操作\\u3002\\s*\\n\\s*###\\s*\\n\\s*### 11\\./g, '- 不需要分组操作。\\n\\n### 11.');\n\n      // 清理重复的排序描述\n      processed = processed.replace(/- 用户没有用户没有指定排序要求，要求，可以按默认可以按默认顺序返回顺序返回结果（结果（通常为主通常为主键顺序）\\u3002\\s*\\n\\s*###\\s*\\n\\s*键顺序）\\u3002\\s*\\n\\s*###\\s*\\n\\s*### 12/g, '- 用户没有指定排序要求，可以按默认顺序返回结果（通常为主键顺序）。\\n\\n### 12');\n\n      // 清理SQL语句中的错误\n      processed = processed.replace(/```sql\\s*([^`]*)```/g, (match, sqlContent) => {\n        // 清理SQL代码块内的格式问题\n        let cleanedSql = sqlContent\n          .replace(/\\s*SELECT\\s+SELECT\\s*/g, 'SELECT ')\n          .replace(/\\s*FROM\\s+FROM\\s*/g, 'FROM ')\n          .replace(/(\\w+)\\s+\\1/g, '$1')\n          .replace(/\\s+;\\s+/g, ';\\n')\n          .replace(/--\\s*\\n\\s*--/g, '--')\n          .replace(/SELECT\\s*\\n\\s*SELECT/g, 'SELECT')\n          .replace(/FROM students\\s*\\n\\s*FROM students/g, 'FROM students')\n          .replace(/student_id student_id/g, 'student_id')\n          .replace(/student_name, student_name/g, 'student_name')\n          .replace(/major, major/g, 'major')\n          .replace(/year_of_enrollment, year_of_enrollment/g, 'year_of_enrollment')\n          .replace(/student_age\\s*\\n\\s*FROM/g, 'student_age\\nFROM')\n          .trim();\n\n        return '```sql\\n' + cleanedSql + '\\n```';\n      });\n\n      // 最后清理多余的空行\n      processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n      processed = processed.replace(/```\\s*\\n\\s*\\n/g, '```\\n');\n      processed = processed.replace(/\\n\\s*\\n```/g, '\\n```');\n      processed = processed.replace(/;\\s*\\n\\s*```Enter/g, ';\\n```\\n\\nEnter');\n    }\n\n    return processed;\n  };\n\n  // 预处理内容\n  const processedContent = preprocessContent(content, type);\n\n  try {\n    switch (type) {\n      case 'json':\n        try {\n          // 尝试解析JSON\n          const parsedJson = JSON.parse(processedContent);\n          return (\n            <SyntaxHighlighter\n              language=\"json\"\n              style={codeTheme}\n              showLineNumbers={true}\n              startingLineNumber={1}\n              wrapLines={true}\n              wrapLongLines={true}\n            >\n              {JSON.stringify(parsedJson, null, 2)}\n            </SyntaxHighlighter>\n          );\n        } catch (e) {\n          // 如果解析失败，作为普通文本显示\n          return <div className=\"whitespace-pre-wrap\">{processedContent}</div>;\n        }\n\n      case 'sql':\n        return (\n          <SyntaxHighlighter\n            language=\"sql\"\n            style={codeTheme}\n            showLineNumbers={true}\n            startingLineNumber={1}\n            wrapLines={true}\n            wrapLongLines={true}\n          >\n            {processedContent}\n          </SyntaxHighlighter>\n        );\n\n      case 'markdown':\n        try {\n          return (\n            <ReactMarkdown\n              // 暂时移除 remarkPlugins 以避免类型错误\n              // remarkPlugins={[remarkGfm]}\n              components={{\n                pre({ node, ...props }) {\n                  return <pre className=\"rounded-md bg-gray-100 dark:bg-gray-800/70 p-2 my-4 overflow-auto\" {...props} />;\n                },\n                code({ node, className, ...props }: any) {\n                  const match = /language-(\\w+)/.exec(className || '');\n                  return !props.inline && match ? (\n                    <SyntaxHighlighter\n                      language={match[1]}\n                      style={codeTheme}\n                      showLineNumbers={true}\n                      startingLineNumber={1}\n                      PreTag=\"div\"\n                      wrapLines={true}\n                      wrapLongLines={true}\n                    >\n                      {String(props.children).replace(/\\n$/, '')}\n                    </SyntaxHighlighter>\n                  ) : (\n                    <code className={`${className || ''} px-1 py-0.5 rounded bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 text-sm font-mono`} {...props} />\n                  );\n                },\n                table({ node, ...props }) {\n                  return (\n                    <div className=\"overflow-x-auto my-6 rounded-md border border-gray-200 dark:border-gray-700\">\n                      <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\" {...props} />\n                    </div>\n                  );\n                },\n                thead({ node, ...props }) {\n                  return <thead className=\"bg-gray-50 dark:bg-gray-800\" {...props} />;\n                },\n                th({ node, ...props }) {\n                  return <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\" {...props} />;\n                },\n                td({ node, ...props }) {\n                  return <td className=\"px-4 py-3 text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700\" {...props} />;\n                },\n                h1({ node, children, ...props }) {\n                  return <h1 className=\"text-2xl font-bold mt-6 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700\" {...props}>{children}</h1>;\n                },\n                h2({ node, children, ...props }) {\n                  return <h2 className=\"text-xl font-bold mt-5 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700\" {...props}>{children}</h2>;\n                },\n                h3({ node, children, ...props }) {\n                  return <h3 className=\"text-lg font-bold mt-4 mb-2\" {...props}>{children}</h3>;\n                },\n                h4({ node, children, ...props }) {\n                  return <h4 className=\"text-base font-semibold mt-3 mb-2\" {...props}>{children}</h4>;\n                },\n                ul({ node, ...props }) {\n                  return <ul className=\"list-disc pl-6 my-4 space-y-2\" {...props} />;\n                },\n                ol({ node, start, ...props }: any) {\n                  return <ol className=\"list-decimal pl-6 my-4 space-y-2\" start={start || 1} {...props} />;\n                },\n                li({ node, ...props }) {\n                  return <li className=\"my-1\" {...props} />;\n                },\n                p({ node, ...props }) {\n                  return <p className=\"my-4 leading-relaxed\" {...props} />;\n                },\n                blockquote({ node, ...props }) {\n                  return <blockquote className=\"pl-4 border-l-4 border-gray-200 dark:border-gray-700 my-4 italic text-gray-600 dark:text-gray-300\" {...props} />;\n                },\n                hr({ node, ...props }) {\n                  return <hr className=\"my-6 border-gray-200 dark:border-gray-700\" {...props} />;\n                },\n                a({ node, children, ...props }) {\n                  return <a className=\"text-blue-600 dark:text-blue-400 hover:underline\" {...props}>{children}</a>;\n                },\n                img({ node, alt, ...props }) {\n                  return <img className=\"max-w-full h-auto my-4 rounded-md\" alt={alt || \"图片\"} {...props} />;\n                },\n              }}\n            >\n              {processedContent}\n            </ReactMarkdown>\n          );\n        } catch (error) {\n          return (\n            <div className=\"whitespace-pre-wrap p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800/50 rounded-md\">\n              <p className=\"text-red-500 dark:text-red-400 font-bold mb-2\">Markdown渲染错误</p>\n              <div className=\"overflow-auto max-h-[300px] font-mono text-sm\">{processedContent}</div>\n            </div>\n          );\n        }\n\n      default:\n        return <div className=\"whitespace-pre-wrap leading-relaxed\">{processedContent}</div>;\n    }\n  } catch (error) {\n    return <div className=\"whitespace-pre-wrap text-red-500 p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800/50 rounded-md\">\n      <p className=\"font-bold mb-2\">渲染错误</p>\n      <div className=\"overflow-auto max-h-[300px] font-mono text-sm\">{processedContent}</div>\n    </div>;\n  }\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,gBAAgB;AAC1C,SAASC,KAAK,IAAIC,iBAAiB,QAAQ,0BAA0B;AACrE,SAASC,IAAI,IAAIC,SAAS,QAAQ,gDAAgD;AAClF;AACA;;AAEA;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,OAAO,MAAMC,YAAY,GAAIC,IAAW,IAAa;EACnD,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE,OAAO,EAAE;;EAEpC;EACA,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEpC;EACA,MAAMK,SAAS,GAAGH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC;;EAEnC;EACA,MAAMC,IAAI,GAAGP,IAAI,CAACQ,GAAG,CAACC,GAAG,IAAI;IAC3B,OAAOP,OAAO,CAACM,GAAG,CAACE,MAAM,IAAI;MAC3B;MACA,MAAMC,KAAK,GAAGF,GAAG,CAACC,MAAM,CAAC;MACzB,MAAME,QAAQ,GAAGD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,GAAG,EAAE,GAAGC,MAAM,CAACH,KAAK,CAAC;MAC3E;MACA,IAAIC,QAAQ,CAACG,QAAQ,CAAC,GAAG,CAAC,IAAIH,QAAQ,CAACG,QAAQ,CAAC,GAAG,CAAC,IAAIH,QAAQ,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/E,OAAO,IAAIH,QAAQ,CAACI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;MAC5C;MACA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,CAACN,IAAI,CAAC,GAAG,CAAC;EACd,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;;EAEb;EACA,OAAO,GAAGD,SAAS,KAAKE,IAAI,EAAE;AAChC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMU,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC,IAAI;EAAEC;AAAyF,CAAC,KAAK;EAC9I,IAAI,CAACF,OAAO,EAAE;IACZ,oBAAOpB,OAAA;MAAKuB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzE;;EAEA;EACA,MAAMC,iBAAiB,GAAGA,CAACC,UAAkB,EAAEC,WAAmB,KAAa;IAC7E,IAAIC,SAAS,GAAGF,UAAU;;IAE1B;IACA,IAAIC,WAAW,KAAK,UAAU,EAAE;MAC9B;MACA,IAAIT,MAAM,KAAK,aAAa,EAAE;QAC5B;QACAU,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;QAClDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;QACjD,OAAOc,SAAS;MAClB;MACA;MACAA,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,+CAA+C,EAAE,EAAE,CAAC;MAClF;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wCAAwC,EAAE,uBAAuB,CAAC;MAChGc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;;MAE3C;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC;MAC7Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iCAAiC,EAAE,gBAAgB,CAAC;MAClFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;;MAE/C;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,uBAAuB,EAAE,qBAAqB,CAAC;MAC7Ec,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC;MAC1Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wBAAwB,EAAE,wBAAwB,CAAC;;MAEjF;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,oBAAoB,EAAE,QAAQ,CAAC;MAC7Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,kBAAkB,EAAE,SAAS,CAAC;MAC5Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;MAClDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;MAClDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC;MACpDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC;MACtDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC;MACtDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,WAAW,CAAC;MAC5Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC;MACtDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;MAC1Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;MAC1Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,oBAAoB,EAAE,WAAW,CAAC;MAChEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,sBAAsB,EAAE,cAAc,CAAC;MACrEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,2BAA2B,EAAE,kBAAkB,CAAC;;MAE9E;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,sDAAsD,EAAE,yBAAyB,CAAC;MAChHc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wCAAwC,EAAE,MAAM,CAAC;MAC/Ec,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,mEAAmE,EAAE,iCAAiC,CAAC;MACrIc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wBAAwB,EAAE,2BAA2B,CAAC;MACpFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,qCAAqC,EAAE,iBAAiB,CAAC;MACvFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;MACrDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,+CAA+C,EAAE,qBAAqB,CAAC;MACrGc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC;MACzDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gCAAgC,EAAE,oBAAoB,CAAC;MACrFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gCAAgC,EAAE,uBAAuB,CAAC;MACxFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,6CAA6C,EAAE,sBAAsB,CAAC;MACpGc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC;MACrDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wEAAwE,EAAE,+CAA+C,CAAC;MACxJc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;MAC5Cc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,yBAAyB,EAAE,aAAa,CAAC;MACvEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC;MAC3Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,qBAAqB,EAAE,YAAY,CAAC;;MAElE;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,uBAAuB,EAAE,QAAQ,CAAC;MAChEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,0CAA0C,EAAE,oBAAoB,CAAC;MAC/Fc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,4BAA4B,EAAE,cAAc,CAAC;MAC3Ec,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,2BAA2B,EAAE,oBAAoB,CAAC;MAChFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,0BAA0B,EAAE,aAAa,CAAC;MACxEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,qCAAqC,EAAE,eAAe,CAAC;MACrFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEtD;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iCAAiC,EAAE,uBAAuB,CAAC;MACzFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,0CAA0C,EAAE,qBAAqB,CAAC;MAChGc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,8BAA8B,EAAE,uBAAuB,CAAC;MACtFc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,oCAAoC,EAAE,aAAa,CAAC;;MAElF;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC;MAC/Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,6CAA6C,EAAE,aAAa,CAAC;MAC3Fc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC;;MAEvD;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MACnDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MACnDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAC3Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC;MACzDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;MAClDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC;MACzDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;MACtDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC;MAC1Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC;MAC1Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC;MACxDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;;MAEtE;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,oBAAoB,EAAE,cAAc,CAAC;;MAEnE;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;MAEhD;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wBAAwB,EAAE,QAAQ,CAAC;MACjEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wBAAwB,EAAE,QAAQ,CAAC;;MAEjE;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,qCAAqC,EAAE,UAAU,CAAC;;MAEhF;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAC3Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;;MAE3D;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;;MAEtD;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,2BAA2B,EAAE,SAAS,CAAC;;MAErE;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC;;MAE1D;MACA;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC;MAC/Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,+BAA+B,EAAE,IAAI,CAAC;MACpEc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,6CAA6C,EAAE,aAAa,CAAC;MAC3Fc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,kDAAkD,EAAE,aAAa,CAAC;;MAEhG;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC;MACvDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,sBAAsB,EAAE,SAAS,CAAC;;MAEhE;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC;MAC7Dc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gCAAgC,EAAE,eAAe,CAAC;;MAEhF;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,sGAAsG,EAAE,iFAAiF,CAAC;;MAExN;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,8DAA8D,EAAE,gCAAgC,CAAC;;MAE/H;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gEAAgE,EAAE,gCAAgC,CAAC;;MAEjI;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,6EAA6E,EAAE,uBAAuB,CAAC;;MAErI;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,uHAAuH,EAAE,8CAA8C,CAAC;;MAEtM;MACAc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,sBAAsB,EAAE,CAACe,KAAK,EAAEC,UAAU,KAAK;QAC3E;QACA,IAAIC,UAAU,GAAGD,UAAU,CACxBhB,OAAO,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAC5CA,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CACtCA,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAC5BA,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAC1BA,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAC9BA,OAAO,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAC1CA,OAAO,CAAC,qCAAqC,EAAE,eAAe,CAAC,CAC/DA,OAAO,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAC/CA,OAAO,CAAC,6BAA6B,EAAE,cAAc,CAAC,CACtDA,OAAO,CAAC,eAAe,EAAE,OAAO,CAAC,CACjCA,OAAO,CAAC,yCAAyC,EAAE,oBAAoB,CAAC,CACxEA,OAAO,CAAC,0BAA0B,EAAE,mBAAmB,CAAC,CACxDkB,IAAI,CAAC,CAAC;QAET,OAAO,UAAU,GAAGD,UAAU,GAAG,OAAO;MAC1C,CAAC,CAAC;;MAEF;MACAH,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;MAChDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC;MACxDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC;MACrDc,SAAS,GAAGA,SAAS,CAACd,OAAO,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;IACxE;IAEA,OAAOc,SAAS;EAClB,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAGR,iBAAiB,CAACT,OAAO,EAAEC,IAAI,CAAC;EAEzD,IAAI;IACF,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,IAAI;UACF;UACA,MAAMiB,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,gBAAgB,CAAC;UAC/C,oBACErC,OAAA,CAACJ,iBAAiB;YAChB6C,QAAQ,EAAC,MAAM;YACfC,KAAK,EAAE5C,SAAU;YACjB6C,eAAe,EAAE,IAAK;YACtBC,kBAAkB,EAAE,CAAE;YACtBC,SAAS,EAAE,IAAK;YAChBC,aAAa,EAAE,IAAK;YAAAtB,QAAA,EAEnBe,IAAI,CAACQ,SAAS,CAACT,UAAU,EAAE,IAAI,EAAE,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAExB,CAAC,CAAC,OAAOoB,CAAC,EAAE;UACV;UACA,oBAAOhD,OAAA;YAAKuB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEa;UAAgB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QACtE;MAEF,KAAK,KAAK;QACR,oBACE5B,OAAA,CAACJ,iBAAiB;UAChB6C,QAAQ,EAAC,KAAK;UACdC,KAAK,EAAE5C,SAAU;UACjB6C,eAAe,EAAE,IAAK;UACtBC,kBAAkB,EAAE,CAAE;UACtBC,SAAS,EAAE,IAAK;UAChBC,aAAa,EAAE,IAAK;UAAAtB,QAAA,EAEnBa;QAAgB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAGxB,KAAK,UAAU;QACb,IAAI;UACF,oBACE5B,OAAA,CAACN;UACC;UACA;UAAA;YACAuD,UAAU,EAAE;cACVC,GAAGA,CAAC;gBAAEC,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACtB,oBAAOpD,OAAA;kBAAKuB,SAAS,EAAC,mEAAmE;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cACzG,CAAC;cACDyB,IAAIA,CAAC;gBAAEF,IAAI;gBAAE5B,SAAS;gBAAE,GAAG6B;cAAW,CAAC,EAAE;gBACvC,MAAMnB,KAAK,GAAG,gBAAgB,CAACqB,IAAI,CAAC/B,SAAS,IAAI,EAAE,CAAC;gBACpD,OAAO,CAAC6B,KAAK,CAACG,MAAM,IAAItB,KAAK,gBAC3BjC,OAAA,CAACJ,iBAAiB;kBAChB6C,QAAQ,EAAER,KAAK,CAAC,CAAC,CAAE;kBACnBS,KAAK,EAAE5C,SAAU;kBACjB6C,eAAe,EAAE,IAAK;kBACtBC,kBAAkB,EAAE,CAAE;kBACtBY,MAAM,EAAC,KAAK;kBACZX,SAAS,EAAE,IAAK;kBAChBC,aAAa,EAAE,IAAK;kBAAAtB,QAAA,EAEnBR,MAAM,CAACoC,KAAK,CAAC5B,QAAQ,CAAC,CAACN,OAAO,CAAC,KAAK,EAAE,EAAE;gBAAC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,gBAEpB5B,OAAA;kBAAMuB,SAAS,EAAE,GAAGA,SAAS,IAAI,EAAE,sGAAuG;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxJ;cACH,CAAC;cACD6B,KAAKA,CAAC;gBAAEN,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACxB,oBACEpD,OAAA;kBAAKuB,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAC1FxB,OAAA;oBAAOuB,SAAS,EAAC,0DAA0D;oBAAA,GAAK6B;kBAAK;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAEV,CAAC;cACD8B,KAAKA,CAAC;gBAAEP,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACxB,oBAAOpD,OAAA;kBAAOuB,SAAS,EAAC,6BAA6B;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cACrE,CAAC;cACD+B,EAAEA,CAAC;gBAAER,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACrB,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,mGAAmG;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cACxI,CAAC;cACDgC,EAAEA,CAAC;gBAAET,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACrB,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,kGAAkG;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cACvI,CAAC;cACDiC,EAAEA,CAAC;gBAAEV,IAAI;gBAAE3B,QAAQ;gBAAE,GAAG4B;cAAM,CAAC,EAAE;gBAC/B,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,iFAAiF;kBAAA,GAAK6B,KAAK;kBAAA5B,QAAA,EAAGA;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACnI,CAAC;cACDkC,EAAEA,CAAC;gBAAEX,IAAI;gBAAE3B,QAAQ;gBAAE,GAAG4B;cAAM,CAAC,EAAE;gBAC/B,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,gFAAgF;kBAAA,GAAK6B,KAAK;kBAAA5B,QAAA,EAAGA;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAClI,CAAC;cACDmC,EAAEA,CAAC;gBAAEZ,IAAI;gBAAE3B,QAAQ;gBAAE,GAAG4B;cAAM,CAAC,EAAE;gBAC/B,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,6BAA6B;kBAAA,GAAK6B,KAAK;kBAAA5B,QAAA,EAAGA;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAC/E,CAAC;cACDoC,EAAEA,CAAC;gBAAEb,IAAI;gBAAE3B,QAAQ;gBAAE,GAAG4B;cAAM,CAAC,EAAE;gBAC/B,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,mCAAmC;kBAAA,GAAK6B,KAAK;kBAAA5B,QAAA,EAAGA;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cACrF,CAAC;cACDqC,EAAEA,CAAC;gBAAEd,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACrB,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,+BAA+B;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cACpE,CAAC;cACDsC,EAAEA,CAAC;gBAAEf,IAAI;gBAAEgB,KAAK;gBAAE,GAAGf;cAAW,CAAC,EAAE;gBACjC,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,kCAAkC;kBAAC4C,KAAK,EAAEA,KAAK,IAAI,CAAE;kBAAA,GAAKf;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAC1F,CAAC;cACDwC,EAAEA,CAAC;gBAAEjB,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACrB,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,MAAM;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAC3C,CAAC;cACDyC,CAACA,CAAC;gBAAElB,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACpB,oBAAOpD,OAAA;kBAAGuB,SAAS,EAAC,sBAAsB;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAC1D,CAAC;cACD0C,UAAUA,CAAC;gBAAEnB,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBAC7B,oBAAOpD,OAAA;kBAAYuB,SAAS,EAAC,mGAAmG;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAChJ,CAAC;cACD2C,EAAEA,CAAC;gBAAEpB,IAAI;gBAAE,GAAGC;cAAM,CAAC,EAAE;gBACrB,oBAAOpD,OAAA;kBAAIuB,SAAS,EAAC,2CAA2C;kBAAA,GAAK6B;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAChF,CAAC;cACD4C,CAACA,CAAC;gBAAErB,IAAI;gBAAE3B,QAAQ;gBAAE,GAAG4B;cAAM,CAAC,EAAE;gBAC9B,oBAAOpD,OAAA;kBAAGuB,SAAS,EAAC,kDAAkD;kBAAA,GAAK6B,KAAK;kBAAA5B,QAAA,EAAGA;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAClG,CAAC;cACD6C,GAAGA,CAAC;gBAAEtB,IAAI;gBAAEuB,GAAG;gBAAE,GAAGtB;cAAM,CAAC,EAAE;gBAC3B,oBAAOpD,OAAA;kBAAKuB,SAAS,EAAC,mCAAmC;kBAACmD,GAAG,EAAEA,GAAG,IAAI,IAAK;kBAAA,GAAKtB;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAC3F;YACF,CAAE;YAAAJ,QAAA,EAEDa;UAAgB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAEpB,CAAC,CAAC,OAAO+C,KAAK,EAAE;UACd,oBACE3E,OAAA;YAAKuB,SAAS,EAAC,8GAA8G;YAAAC,QAAA,gBAC3HxB,OAAA;cAAGuB,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7E5B,OAAA;cAAKuB,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAEa;YAAgB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAEV;MAEF;QACE,oBAAO5B,OAAA;UAAKuB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAEa;QAAgB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACxF;EACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;IACd,oBAAO3E,OAAA;MAAKuB,SAAS,EAAC,2HAA2H;MAAAC,QAAA,gBAC/IxB,OAAA;QAAGuB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtC5B,OAAA;QAAKuB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAAEa;MAAgB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF,CAAC;EACR;AACF,CAAC;AAACgD,EAAA,GA5UWzD,eAAe;AAAA,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}